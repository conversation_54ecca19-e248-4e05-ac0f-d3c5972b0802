# Flori Construction Ltd - Website & Mobile Admin App

A comprehensive construction company website with a modern design, admin panel, and React Native mobile application for content management.

## Features

### Website Features
- **Modern Responsive Design** - Clean, professional layout optimized for all devices
- **Homepage** - Hero section, services overview, recent projects showcase
- **About Us** - Company story, values, team, and statistics
- **Services** - Detailed service pages (Civil Engineering, Groundworks, RC Frames, Basements, Hard Landscaping)
- **Projects** - Project gallery with filtering (completed/ongoing)
- **Media Gallery** - Photo and video showcase
- **Contact Form** - Integrated contact form with message management
- **SEO Optimized** - Meta tags, structured data, and search engine friendly URLs

### Admin Panel Features
- **Dashboard** - Statistics overview and recent activity
- **Project Management** - CRUD operations for projects
- **Service Management** - Manage service offerings
- **Media Management** - Upload and organize images/videos
- **Message Management** - View and respond to contact form submissions
- **Page Management** - Edit website pages (homepage, about, contact, etc.)
- **User Management** - Admin user accounts
- **Settings** - Company information and social media links

### Mobile App Features (React Native)
- **Cross-platform** - iOS and Android support
- **Authentication** - Secure login with JWT tokens
- **Project Management** - Add, edit, delete projects on the go
- **Service Management** - Manage services from mobile device
- **Message Viewing** - Check and respond to customer messages
- **Media Upload** - Upload photos directly from mobile camera
- **Dashboard** - Key metrics and recent activity
- **Offline Support** - Basic functionality when offline

## Technology Stack

### Backend
- **PHP 7.4+** - Server-side scripting
- **MySQL 5.7+** - Database management
- **PDO** - Database abstraction layer
- **JWT** - Authentication for mobile API

### Frontend (Website)
- **HTML5/CSS3** - Modern markup and styling
- **Bootstrap 5** - Responsive framework
- **JavaScript (ES6+)** - Interactive functionality
- **AOS** - Scroll animations
- **Font Awesome** - Icons

### Mobile App
- **React Native** - Cross-platform mobile development
- **Expo** - Development platform and tools
- **React Navigation** - Navigation library
- **React Native Paper** - Material Design components
- **Axios** - HTTP client for API calls
- **Expo SecureStore** - Secure token storage

## Installation & Setup

### Prerequisites
- XAMPP/WAMP/LAMP server with PHP 7.4+ and MySQL 5.7+
- Node.js 16+ and npm
- Expo CLI for mobile development

### Website Setup

1. **Clone/Download the project** to your web server directory:
   ```bash
   cd c:\xampp\htdocs\
   # Copy the project files here
   ```

2. **Database Setup**:
   - Start Apache and MySQL in XAMPP
   - Create a new database named `construction_website`
   - The database tables will be created automatically when you first access the site

3. **Configuration**:
   - Edit `config/database.php` if needed to match your database settings
   - Default settings work with XAMPP out of the box

4. **Access the Website**:
   - Website: `http://localhost/mobile-web-app/`
   - Admin Panel: `http://localhost/mobile-web-app/admin/`
   - Default admin credentials: `admin` / `admin123`

### Mobile App Setup

1. **Install Dependencies**:
   ```bash
   cd mobile-app
   npm install
   ```

2. **Install Expo CLI** (if not already installed):
   ```bash
   npm install -g @expo/cli
   ```

3. **Configure API URL**:
   - Edit `mobile-app/src/services/apiService.js`
   - Update `API_BASE_URL` to match your server URL
   - For local development: `http://your-ip-address/mobile-web-app/api`

4. **Start the Development Server**:
   ```bash
   npm start
   ```

5. **Run on Device/Emulator**:
   - Install Expo Go app on your mobile device
   - Scan the QR code displayed in the terminal
   - Or use Android/iOS emulator

## Default Credentials

### Admin Panel
- **Username**: `admin`
- **Password**: `admin123`

### Mobile App
- Use the same credentials as the admin panel

## API Endpoints

The mobile app communicates with the website through REST API endpoints:

### Authentication
- `POST /api/auth.php?action=login` - User login
- `POST /api/auth.php?action=logout` - User logout
- `GET /api/auth.php?action=verify` - Verify token

### Projects
- `GET /api/projects.php` - Get projects list
- `POST /api/projects.php` - Create new project
- `PUT /api/projects.php?id={id}` - Update project
- `DELETE /api/projects.php?id={id}` - Delete project

### Services
- `GET /api/services.php` - Get services list
- `POST /api/services.php` - Create new service
- `PUT /api/services.php?id={id}` - Update service
- `DELETE /api/services.php?id={id}` - Delete service

### Messages
- `GET /api/messages.php` - Get messages list
- `POST /api/messages.php?action=contact` - Submit contact form
- `PUT /api/messages.php?id={id}` - Update message status
- `DELETE /api/messages.php?id={id}` - Delete message

## File Structure

```
mobile-web-app/
├── config/
│   └── database.php          # Database configuration
├── includes/
│   ├── functions.php         # Common PHP functions
│   ├── header.php           # Website header
│   └── footer.php           # Website footer
├── admin/
│   ├── login.php            # Admin login
│   ├── dashboard.php        # Admin dashboard
│   └── includes/            # Admin panel components
├── api/
│   ├── auth.php             # Authentication API
│   ├── projects.php         # Projects API
│   ├── services.php         # Services API
│   └── messages.php         # Messages API
├── css/
│   └── style.css            # Main stylesheet
├── js/
│   └── main.js              # Main JavaScript
├── assets/
│   └── images/              # Website images
├── uploads/                 # User uploaded files
├── index.php                # Homepage
├── about.php                # About page
├── contact.php              # Contact page
└── mobile-app/
    ├── src/
    │   ├── screens/         # React Native screens
    │   ├── context/         # React context providers
    │   ├── services/        # API services
    │   └── theme/           # App theme
    ├── App.js               # Main app component
    └── package.json         # Dependencies
```

## Customization

### Website Branding
1. Replace logo images in `assets/images/`
2. Update company information in the database settings
3. Modify colors in `css/style.css` (CSS variables at the top)
4. Update social media links in admin settings

### Mobile App Branding
1. Update app name in `mobile-app/package.json`
2. Modify colors in `mobile-app/src/theme/theme.js`
3. Replace app icon and splash screen (follow Expo documentation)

## Security Considerations

1. **Change Default Credentials** - Update admin password immediately
2. **Database Security** - Use strong database passwords in production
3. **File Permissions** - Set appropriate file permissions on server
4. **HTTPS** - Use SSL certificates in production
5. **API Security** - Implement rate limiting and input validation
6. **JWT Secret** - Change the JWT secret key in `api/auth.php`

## Support & Maintenance

### Regular Maintenance
- Keep PHP and MySQL updated
- Regular database backups
- Monitor server logs for errors
- Update dependencies regularly

### Troubleshooting
- Check PHP error logs for backend issues
- Use browser developer tools for frontend debugging
- Check Expo logs for mobile app issues
- Verify API endpoints are accessible

## License

This project is created for Flori Construction Ltd. All rights reserved.

## Contact

For support or questions about this project, please contact the development team.

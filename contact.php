<?php
require_once 'includes/functions.php';

$pageTitle = 'Contact Us';
$pageDescription = 'Get in touch with Flori Construction Ltd for your construction needs. Professional construction services in London.';

// Handle form submission
$message = '';
$messageType = '';

if ($_POST) {
    try {
        $name = sanitizeInput($_POST['name'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $phone = sanitizeInput($_POST['phone'] ?? '');
        $subject = sanitizeInput($_POST['subject'] ?? '');
        $messageText = sanitizeInput($_POST['message'] ?? '');

        // Validate required fields
        if (empty($name) || empty($email) || empty($messageText)) {
            $message = 'Please fill in all required fields (Name, Email, and Message).';
            $messageType = 'error';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message = 'Please enter a valid email address.';
            $messageType = 'error';
        } else {
            $database = new Database();
            $conn = $database->getConnection();

            if (!$conn) {
                throw new Exception('Database connection failed');
            }

            $stmt = $conn->prepare("INSERT INTO messages (name, email, phone, subject, message, status) VALUES (?, ?, ?, ?, ?, 'new')");
            $result = $stmt->execute([$name, $email, $phone, $subject, $messageText]);

            if ($result) {
                $message = 'Thank you for your message. We will get back to you soon!';
                $messageType = 'success';

                // Clear form data
                $_POST = array();
            } else {
                throw new Exception('Failed to save message');
            }
        }
    } catch (PDOException $e) {
        error_log("Contact form PDO error: " . $e->getMessage());
        $message = 'Sorry, there was a database error. Please try again or contact us directly.';
        $messageType = 'error';
    } catch (Exception $e) {
        error_log("Contact form error: " . $e->getMessage());
        $message = 'Sorry, there was an error sending your message. Please try again.';
        $messageType = 'error';
    }
}

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold mb-3">Contact Us</h1>
                <p class="lead">Get in touch with our construction experts</p>
                <?php echo generateBreadcrumbs([
                    ['title' => 'Home', 'url' => 'index.php'],
                    ['title' => 'Contact Us']
                ]); ?>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="contact-section py-5">
    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <div class="row">
            <!-- Contact Information -->
            <div class="col-lg-4 mb-5" data-aos="fade-right">
                <div class="contact-info">
                    <h3 class="mb-4">Get In Touch</h3>
                    <p class="mb-4">Ready to start your construction project? Contact us today for a free consultation and quote.</p>
                    
                    <div class="contact-item mb-4">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h5>Address</h5>
                            <p><?php echo getSetting('company_address'); ?></p>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-4">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h5>Phone</h5>
                            <p>
                                <a href="tel:<?php echo str_replace(' ', '', getSetting('company_phone')); ?>" class="text-decoration-none">
                                    <?php echo getSetting('company_phone'); ?>
                                </a>
                            </p>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-4">
                        <div class="contact-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h5>Mobile</h5>
                            <p>
                                <a href="tel:<?php echo str_replace(' ', '', getSetting('company_mobile')); ?>" class="text-decoration-none">
                                    <?php echo getSetting('company_mobile'); ?>
                                </a>
                            </p>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-4">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h5>Email</h5>
                            <p>
                                <a href="mailto:<?php echo getSetting('company_email'); ?>" class="text-decoration-none">
                                    <?php echo getSetting('company_email'); ?>
                                </a>
                            </p>
                        </div>
                    </div>
                    
                    <div class="social-links mt-4">
                        <h5 class="mb-3">Follow Us</h5>
                        <a href="<?php echo getSetting('facebook_url'); ?>" class="social-link me-3" target="_blank">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="<?php echo getSetting('instagram_url'); ?>" class="social-link me-3" target="_blank">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="<?php echo getSetting('youtube_url'); ?>" class="social-link me-3" target="_blank">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="<?php echo getSetting('linkedin_url'); ?>" class="social-link" target="_blank">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Contact Form -->
            <div class="col-lg-8" data-aos="fade-left">
                <div class="contact-form-wrapper">
                    <h3 class="mb-4">Send Us a Message</h3>
                    <form id="contactForm" method="POST" action="contact.php" class="contact-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">Subject</label>
                                <select class="form-control" id="subject" name="subject">
                                    <option value="General Inquiry">General Inquiry</option>
                                    <option value="Project Quote">Project Quote</option>
                                    <option value="Civil Engineering">Civil Engineering</option>
                                    <option value="Groundworks">Groundworks</option>
                                    <option value="RC Frames">RC Frames</option>
                                    <option value="Basement Construction">Basement Construction</option>
                                    <option value="Hard Landscaping">Hard Landscaping</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" id="message" name="message" rows="6" 
                                      placeholder="Tell us about your project..." required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane"></i> Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="map-section">
    <div class="container-fluid p-0">
        <div class="map-container">
            <iframe 
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2482.1234567890123!2d-0.1234567890123456!3d51.61234567890123!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNTHCsDM2JzQ0LjQiTiAwwrAwNyc0NC40Ilc!5e0!3m2!1sen!2suk!4v1234567890123"
                width="100%" 
                height="400" 
                style="border:0;" 
                allowfullscreen="" 
                loading="lazy" 
                referrerpolicy="no-referrer-when-downgrade">
            </iframe>
        </div>
    </div>
</section>

<style>
.contact-info {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.contact-icon i {
    color: white;
    font-size: 1.2rem;
}

.contact-details h5 {
    margin-bottom: 0.5rem;
    color: var(--secondary-color);
    font-weight: 600;
}

.contact-details p {
    margin-bottom: 0;
    color: var(--text-color);
}

.contact-form-wrapper {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
}

.social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--accent-color);
    transform: translateY(-3px);
    color: white;
}

.map-container {
    position: relative;
    overflow: hidden;
}

.page-header {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
}
</style>

<?php include 'includes/footer.php'; ?>

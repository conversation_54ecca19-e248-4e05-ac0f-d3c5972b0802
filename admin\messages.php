<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

$pageTitle = 'Message Management';

// Handle status update
if (isset($_GET['mark']) && isset($_GET['id']) && is_numeric($_GET['id'])) {
    $messageId = (int)$_GET['id'];
    $status = sanitizeInput($_GET['mark']);
    
    if (in_array($status, ['read', 'replied'])) {
        $database = new Database();
        $conn = $database->getConnection();
        
        try {
            $stmt = $conn->prepare("UPDATE messages SET status = ? WHERE id = ?");
            $stmt->execute([$status, $messageId]);
            $message = 'Message status updated successfully!';
            $messageType = 'success';
        } catch (PDOException $e) {
            $message = 'Error updating message: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Handle delete request
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $messageId = (int)$_GET['delete'];
    $database = new Database();
    $conn = $database->getConnection();
    
    try {
        $stmt = $conn->prepare("DELETE FROM messages WHERE id = ?");
        $stmt->execute([$messageId]);
        $message = 'Message deleted successfully!';
        $messageType = 'success';
    } catch (PDOException $e) {
        $message = 'Error deleting message: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Get filter
$statusFilter = isset($_GET['status']) ? sanitizeInput($_GET['status']) : null;

// Get all messages
$allMessages = getMessages($statusFilter);

include 'includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Message Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="messages.php" class="btn btn-sm btn-outline-secondary <?php echo !$statusFilter ? 'active' : ''; ?>">All</a>
                        <a href="messages.php?status=new" class="btn btn-sm btn-outline-primary <?php echo $statusFilter == 'new' ? 'active' : ''; ?>">New</a>
                        <a href="messages.php?status=read" class="btn btn-sm btn-outline-info <?php echo $statusFilter == 'read' ? 'active' : ''; ?>">Read</a>
                        <a href="messages.php?status=replied" class="btn btn-sm btn-outline-success <?php echo $statusFilter == 'replied' ? 'active' : ''; ?>">Replied</a>
                    </div>
                </div>
            </div>

            <?php if (isset($message)): ?>
                <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Messages Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <?php if ($statusFilter): ?>
                            <?php echo ucfirst($statusFilter); ?> Messages
                        <?php else: ?>
                            All Messages
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($allMessages)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped data-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Contact</th>
                                        <th>Subject</th>
                                        <th>Message</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($allMessages as $msg): ?>
                                        <tr class="<?php echo $msg['status'] == 'new' ? 'table-warning' : ''; ?>">
                                            <td><?php echo $msg['id']; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($msg['name']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-envelope"></i> <?php echo htmlspecialchars($msg['email']); ?>
                                                </small>
                                                <?php if ($msg['phone']): ?>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-phone"></i> <?php echo htmlspecialchars($msg['phone']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($msg['subject'] ?: 'No Subject'); ?></td>
                                            <td>
                                                <div class="message-preview">
                                                    <?php echo htmlspecialchars(substr($msg['message'], 0, 100)) . (strlen($msg['message']) > 100 ? '...' : ''); ?>
                                                </div>
                                                <button class="btn btn-sm btn-link p-0" onclick="showFullMessage(<?php echo $msg['id']; ?>)">
                                                    Read Full Message
                                                </button>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $msg['status'] == 'new' ? 'warning' : 
                                                        ($msg['status'] == 'read' ? 'info' : 'success'); 
                                                ?>">
                                                    <?php echo ucfirst($msg['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M d, Y H:i', strtotime($msg['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if ($msg['status'] == 'new'): ?>
                                                        <a href="messages.php?mark=read&id=<?php echo $msg['id']; ?>" class="btn btn-sm btn-outline-info" title="Mark as Read">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($msg['status'] != 'replied'): ?>
                                                        <a href="mailto:<?php echo htmlspecialchars($msg['email']); ?>?subject=Re: <?php echo urlencode($msg['subject']); ?>" 
                                                           class="btn btn-sm btn-outline-success" title="Reply" 
                                                           onclick="markAsReplied(<?php echo $msg['id']; ?>)">
                                                            <i class="fas fa-reply"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteMessage(<?php echo $msg['id']; ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                            <h4>No Messages Found</h4>
                            <p class="text-muted">
                                <?php if ($statusFilter): ?>
                                    No <?php echo $statusFilter; ?> messages at the moment.
                                <?php else: ?>
                                    No messages have been received yet.
                                <?php endif; ?>
                            </p>
                            <a href="messages.php" class="btn btn-primary">View All Messages</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Message Modal -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Full Message</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="messageContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="replyButton">Reply</button>
            </div>
        </div>
    </div>
</div>

<script>
// Store messages data for modal
const messagesData = <?php echo json_encode($allMessages); ?>;

function showFullMessage(messageId) {
    const message = messagesData.find(m => m.id == messageId);
    if (message) {
        const content = `
            <div class="mb-3">
                <strong>From:</strong> ${message.name}<br>
                <strong>Email:</strong> ${message.email}<br>
                ${message.phone ? '<strong>Phone:</strong> ' + message.phone + '<br>' : ''}
                <strong>Subject:</strong> ${message.subject || 'No Subject'}<br>
                <strong>Date:</strong> ${new Date(message.created_at).toLocaleString()}
            </div>
            <div class="border-top pt-3">
                <strong>Message:</strong><br>
                <div class="mt-2" style="white-space: pre-wrap;">${message.message}</div>
            </div>
        `;
        
        document.getElementById('messageContent').innerHTML = content;
        document.getElementById('replyButton').onclick = function() {
            window.open(`mailto:${message.email}?subject=Re: ${encodeURIComponent(message.subject || 'Your Inquiry')}`);
            markAsReplied(messageId);
        };
        
        new bootstrap.Modal(document.getElementById('messageModal')).show();
        
        // Mark as read if it's new
        if (message.status === 'new') {
            setTimeout(() => {
                window.location.href = `messages.php?mark=read&id=${messageId}`;
            }, 1000);
        }
    }
}

function markAsReplied(messageId) {
    setTimeout(() => {
        window.location.href = `messages.php?mark=replied&id=${messageId}`;
    }, 500);
}

function deleteMessage(id) {
    if (confirm('Are you sure you want to delete this message? This action cannot be undone.')) {
        window.location.href = 'messages.php?delete=' + id;
    }
}
</script>

<style>
.message-preview {
    max-width: 300px;
    word-wrap: break-word;
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
}
</style>

<?php include 'includes/admin_footer.php'; ?>
